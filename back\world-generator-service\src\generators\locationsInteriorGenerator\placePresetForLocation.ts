
import { WorldMapCell } from '../../shared/types/World';
import { LocationConfig } from './constants/locationConfig';
import { LocationType } from '../../shared/enums';
import { getPresetsForLocation } from './presets';
import { PresetLocationMap } from './presets/presetType';
import tokenLegend from './presets/tokenLegend';

type LegendMap = Record<string, string | number>;

// Применяет пресеты декораций на локации
export async function placePresetsForLocation(
	cell: WorldMapCell,
	config: LocationConfig,
	rng: () => number,
	legend: LegendMap
): Promise<void> {
	const location = cell.location!;
	if (!location) return;

	// Проверяем тип локации согласно требованиям
	if ((config.type === LocationType.INDOOR || config.type === LocationType.UNDERGROUND)) {
		await applyIndoorUndergroundPreset(location, config, rng, legend);
	} else if (config.type === LocationType.OUTDOOR || config.type === LocationType.BEACH) {
		// Заглушка для outdoor и beach
		console.log(`Preset for ${config.type} not implemented yet`);
	}
}

// Применяет пресет для INDOOR + UNDERGROUND локаций
async function applyIndoorUndergroundPreset(
	location: any,
	config: LocationConfig,
	rng: () => number,
	legend: LegendMap
): Promise<void> {
	// Очищаем существующие декорации, спавн и go back зоны
	clearLocationDecorations(location);

	// Получаем пресеты для данного типа локации
	const presets = getPresetsForLocation(config.type, location.subtype);

	if (!presets || presets.length === 0) {
		console.log(`No presets found for ${config.type} ${location.subtype}`);
		return;
	}

	// Выбираем случайный пресет
	const selectedPreset = selectRandomPreset(presets, rng);

	// Применяем пресет
	applyPresetToLocation(location, selectedPreset, legend);
}

// Очищает декорации и зоны в локации
function clearLocationDecorations(location: any): void {
	// Удаляем все декорации
	location.decorations = {};

	// Очищаем спавн зоны
	location.spawnPosition = [0, 0];

	// Очищаем go back зоны
	location.goBackPosition = [];

	// Меняем тип пресета (не понятно что это, оставляю как есть)
}

// Выбирает случайный пресет из массива
function selectRandomPreset(presets: PresetLocationMap[], rng: () => number): PresetLocationMap {
	const randomIndex = Math.floor(rng() * presets.length);
	return presets[randomIndex];
}

// Применяет выбранный пресет к локации
function applyPresetToLocation(
	location: any,
	preset: PresetLocationMap,
	legend: LegendMap
): void {
	// Очищаем локацию
	location.decorations = {};
	location.goBackPosition = [];
	location.spawnPosition = [0, 0];

	// Устанавливаем размер грида из пресета
	location.locationSize = [preset.width, preset.height];

	// Применяем декорации из tokenMap используя готовый tokenLegend
	applyTokenMapDecorations(location, preset);

	// Находим двери на краях и создаем зоны
	createDoorsAndZones(location, preset);

	console.log(`Applied preset ${preset.name} with size ${preset.width}x${preset.height}`);
}

// Применяет декорации из tokenMap используя готовый tokenLegend
function applyTokenMapDecorations(location: any, preset: PresetLocationMap): void {
	const decorations: any = {};

	// Создаем обратный маппинг из tokenLegend
	const tokenToDecoration: Record<number, string> = {};
	for (const [decorationName, token] of Object.entries(tokenLegend)) {
		tokenToDecoration[token as number] = decorationName;
	}

	// Проходим по tokenMap и собираем декорации
	for (let y = 0; y < preset.height; y++) {
		for (let x = 0; x < preset.width; x++) {
			const token = preset.tokenMap[y][x];
			const tokenNum = typeof token === 'number' ? token : parseInt(token.toString());

			// Пропускаем пустые токены
			if (tokenNum === 999 || tokenNum === 100) continue;

			const decorationName = tokenToDecoration[tokenNum];
			if (decorationName && decorationName !== 'none') {
				if (!decorations[decorationName]) {
					decorations[decorationName] = [];
				}
				decorations[decorationName].push([x, y]);
			}
		}
	}

	location.decorations = decorations;
}

// Создает зоны для дверей
function createDoorsAndZones(location: any, preset: PresetLocationMap): void {
	const doorToken = tokenLegend['door']; // 15
	const doorPositions: [number, number][] = [];

	// Находим двери на краях карты
	for (let y = 0; y < preset.height; y++) {
		for (let x = 0; x < preset.width; x++) {
			const token = preset.tokenMap[y][x];
			const tokenNum = typeof token === 'number' ? token : parseInt(token.toString());

			if (tokenNum === doorToken) {
				// Проверяем, на краю ли дверь
				const isOnEdge = x === 0 || x === preset.width - 1 || y === 0 || y === preset.height - 1;
				if (isOnEdge) {
					doorPositions.push([x, y]);
				}
			}
		}
	}

	// Создаем go back зоны в радиусе 2 от дверей
	const goBackZones: [number, number][] = [];
	for (const [doorX, doorY] of doorPositions) {
		for (let dy = -2; dy <= 2; dy++) {
			for (let dx = -2; dx <= 2; dx++) {
				const x = doorX + dx;
				const y = doorY + dy;
				if (x >= 0 && x < preset.width && y >= 0 && y < preset.height) {
					goBackZones.push([x, y]);
				}
			}
		}
	}

	// Спавн позиция - рядом с первой дверью или в центре
	let spawnPosition: [number, number];
	if (doorPositions.length > 0) {
		const [doorX, doorY] = doorPositions[0];
		spawnPosition = [doorX + 1, doorY + 1]; // Рядом с дверью
	} else {
		spawnPosition = [Math.floor(preset.width / 2), Math.floor(preset.height / 2)];
	}

	location.goBackPosition = goBackZones;
	location.spawnPosition = spawnPosition;
}