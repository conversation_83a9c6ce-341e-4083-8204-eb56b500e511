
import { WorldMapCell } from '../../shared/types/World';
import { LocationConfig } from './constants/locationConfig';
import { LocationType } from '../../shared/enums';
import { getPresetsForLocation } from './presets';
import { PresetLocationMap } from './presets/presetType';

type LegendMap = Record<string, string | number>;

// Применяет пресеты декораций на локации
export async function placePresetsForLocation(
	cell: WorldMapCell,
	config: LocationConfig,
	rng: () => number,
	legend: LegendMap
): Promise<void> {
	const location = cell.location!;
	if (!location) return;

	// Проверяем тип локации согласно требованиям
	if ((config.type === LocationType.INDOOR || config.type === LocationType.UNDERGROUND)) {
		await applyIndoorUndergroundPreset(location, config, rng, legend);
	} else if (config.type === LocationType.OUTDOOR || config.type === LocationType.BEACH) {
		// Заглушка для outdoor и beach
		console.log(`Preset for ${config.type} not implemented yet`);
	}
}

// Применяет пресет для INDOOR + UNDERGROUND локаций
async function applyIndoorUndergroundPreset(
	location: any,
	config: LocationConfig,
	rng: () => number,
	legend: LegendMap
): Promise<void> {
	// Очищаем существующие декорации, спавн и go back зоны
	clearLocationDecorations(location);

	// Получаем пресеты для данного типа локации
	const presets = getPresetsForLocation(config.type, location.subtype);

	if (!presets || presets.length === 0) {
		console.log(`No presets found for ${config.type} ${location.subtype}`);
		return;
	}

	// Выбираем случайный пресет
	const selectedPreset = selectRandomPreset(presets, rng);

	// Применяем пресет
	applyPresetToLocation(location, selectedPreset, legend);
}

// Очищает декорации и зоны в локации
function clearLocationDecorations(location: any): void {
	// Удаляем все декорации
	location.decorations = {};

	// Очищаем спавн зоны
	location.spawnPosition = [0, 0];

	// Очищаем go back зоны
	location.goBackPosition = [];

	// Меняем тип пресета (не понятно что это, оставляю как есть)
}

// Выбирает случайный пресет из массива
function selectRandomPreset(presets: PresetLocationMap[], rng: () => number): PresetLocationMap {
	const randomIndex = Math.floor(rng() * presets.length);
	return presets[randomIndex];
}

// Применяет выбранный пресет к локации
function applyPresetToLocation(
	location: any,
	preset: PresetLocationMap,
	legend: LegendMap
): void {
	// Устанавливаем размер грида из пресета
	location.locationSize = [preset.width, preset.height];

	// Применяем токены из пресета (пока заглушка)
	// TODO: Реализовать применение tokenMap с использованием legend
	console.log(`Applied preset ${preset.name} with size ${preset.width}x${preset.height}`);
}