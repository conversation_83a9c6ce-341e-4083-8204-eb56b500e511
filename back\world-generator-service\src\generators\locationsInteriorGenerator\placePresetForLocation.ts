

import { WorldMapCell } from '../../shared/types/World';
import { LocationConfig } from './constants/locationConfig';
type LegendMap = Record<string, string | number>;

// Простая реализация: выбираем случайный пресет из папки buildings (small/medium/big),
// вычисляем позицию по центру + случайный оффсет в пределах anchor и наносим токены.
export async function placePresetsForLocation(
	cell: WorldMapCell,
	config: LocationConfig,
	rng: () => number,
	legend: LegendMap

): Promise<void> {
	const location = cell.location!;
	if (!location) return;

	return;
}