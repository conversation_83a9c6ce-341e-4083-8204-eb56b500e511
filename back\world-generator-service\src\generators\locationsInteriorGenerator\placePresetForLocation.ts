
import { WorldMapCell } from '../../shared/types/World';
import { LocationConfig } from './constants/locationConfig';
import { LocationType, LocationDecorations } from '../../shared/enums';
import { getPresetsForLocation } from './presets';
import { PresetLocationMap } from './presets/presetType';
import { Point } from '../../shared/types/Location';

type LegendMap = Record<string, string | number>;

// Применяет пресеты декораций на локации
export async function placePresetsForLocation(
	cell: WorldMapCell,
	config: LocationConfig,
	rng: () => number,
	legend: LegendMap
): Promise<void> {
	const location = cell.location!;
	if (!location) return;

	// Проверяем тип локации согласно требованиям
	if ((config.type === LocationType.INDOOR || config.type === LocationType.UNDERGROUND)) {
		await applyIndoorUndergroundPreset(location, config, rng, legend);
	} else if (config.type === LocationType.OUTDOOR || config.type === LocationType.BEACH) {
		// Заглушка для outdoor и beach
		console.log(`Preset for ${config.type} not implemented yet`);
	}
}

// Применяет пресет для INDOOR + UNDERGROUND локаций
async function applyIndoorUndergroundPreset(
	location: any,
	config: LocationConfig,
	rng: () => number,
	legend: LegendMap
): Promise<void> {
	// Очищаем существующие декорации, спавн и go back зоны
	clearLocationDecorations(location);

	// Получаем пресеты для данного типа локации
	const presets = getPresetsForLocation(config.type, location.subtype);

	if (!presets || presets.length === 0) {
		console.log(`No presets found for ${config.type} ${location.subtype}`);
		return;
	}

	// Выбираем случайный пресет
	const selectedPreset = selectRandomPreset(presets, rng);

	// Применяем пресет
	applyPresetToLocation(location, selectedPreset, legend);
}

// Очищает декорации и зоны в локации
function clearLocationDecorations(location: any): void {
	// Удаляем все декорации
	location.decorations = {};

	// Очищаем спавн зоны
	location.spawnPosition = [0, 0];

	// Очищаем go back зоны
	location.goBackPosition = [];

	// Меняем тип пресета (не понятно что это, оставляю как есть)
}

// Выбирает случайный пресет из массива
function selectRandomPreset(presets: PresetLocationMap[], rng: () => number): PresetLocationMap {
	const randomIndex = Math.floor(rng() * presets.length);
	return presets[randomIndex];
}

// Применяет выбранный пресет к локации
function applyPresetToLocation(
	location: any,
	preset: PresetLocationMap,
	legend: LegendMap
): void {
	// Устанавливаем размер грида из пресета
	location.locationSize = [preset.width, preset.height];

	// Применяем декорации из tokenMap
	const decorations = applyTokenMapToDecorations(preset, legend);
	location.decorations = decorations;

	// Находим двери на краях и создаем go back зоны
	const doorPositions = findEdgeDoors(preset, legend);
	const goBackZones = createGoBackZones(doorPositions, preset.width, preset.height);
	location.goBackPosition = goBackZones;

	// Создаем спавн зону рядом с дверями
	const spawnPosition = createSpawnPosition(doorPositions, preset.width, preset.height);
	location.spawnPosition = spawnPosition;

	console.log(`Applied preset ${preset.name} with ${doorPositions.length} doors and ${goBackZones.length} go back zones`);
}

// Конвертирует tokenMap в декорации используя legend
function applyTokenMapToDecorations(preset: PresetLocationMap, legend: LegendMap): Partial<Record<LocationDecorations, Point[]>> {
	const decorations: Partial<Record<LocationDecorations, Point[]>> = {};

	// Создаем обратный маппинг: токен -> название декорации
	const tokenToDecoration: Record<number, string> = {};
	for (const [decorationName, token] of Object.entries(legend)) {
		if (typeof token === 'number') {
			tokenToDecoration[token] = decorationName;
		}
	}

	// Проходим по всем токенам в tokenMap
	for (let y = 0; y < preset.height; y++) {
		for (let x = 0; x < preset.width; x++) {
			const token = preset.tokenMap[y][x];
			const tokenNum = typeof token === 'number' ? token : parseInt(token.toString());

			// Пропускаем пустые токены (999, 100)
			if (tokenNum === 999 || tokenNum === 100) continue;

			const decorationName = tokenToDecoration[tokenNum];
			if (decorationName && decorationName !== 'none') {
				// Конвертируем название в enum значение
				const decorationEnum = getDecorationEnum(decorationName);
				if (decorationEnum) {
					if (!decorations[decorationEnum]) {
						decorations[decorationEnum] = [];
					}
					decorations[decorationEnum]!.push([x, y]);
				}
			}
		}
	}

	return decorations;
}

// Конвертирует строковое название декорации в enum значение
function getDecorationEnum(decorationName: string): LocationDecorations | null {
	// Приводим к правильному формату (camelCase)
	const formatted = decorationName.toLowerCase();

	// Маппинг названий к enum значениям
	const decorationMap: Record<string, LocationDecorations> = {
		'wall': LocationDecorations.WALL,
		'door': LocationDecorations.DOOR,
		'window': LocationDecorations.WINDOW,
		'table': LocationDecorations.TABLE,
		'chair': LocationDecorations.CHAIR,
		'bed': LocationDecorations.BED,
		'cabinet': LocationDecorations.CABINET,
		'shelf': LocationDecorations.SHELF,
		'sofa': LocationDecorations.SOFA,
		'interiorlight': LocationDecorations.INTERIORLIGHT,
		'fridge': LocationDecorations.FRIDGE,
		'oven': LocationDecorations.OVEN,
		'tv': LocationDecorations.TV,
		'toilet': LocationDecorations.TOILET,
		'sink': LocationDecorations.SINK,
		'bath': LocationDecorations.BATH,
		'furniture': LocationDecorations.FURNITURE,
		'subwayturnstile': LocationDecorations.SUBWAYTURNSTILE,
		'poster': LocationDecorations.POSTER,
		'stairs': LocationDecorations.STAIRS,
		'vendingmachine': LocationDecorations.VENDINGMACHINE,
		'terminal': LocationDecorations.TERMINAL,
		'safe': LocationDecorations.SAFE,
		'locker': LocationDecorations.LOCKER,
		'bench': LocationDecorations.BENCH,
		'barrel': LocationDecorations.BARREL,
		'box': LocationDecorations.BOX,
		'litter': LocationDecorations.LITTER,
		'void': LocationDecorations.VOID,
		// Добавить другие маппинги по необходимости
	};

	return decorationMap[formatted] || null;
}

// Находит все двери, которые стоят впритык к краю карты
function findEdgeDoors(preset: PresetLocationMap, legend: LegendMap): Point[] {
	const doorPositions: Point[] = [];

	// Получаем токен для двери из legend
	const doorToken = legend['door'] as number;
	if (!doorToken) return doorPositions;

	// Проверяем все края карты
	for (let y = 0; y < preset.height; y++) {
		for (let x = 0; x < preset.width; x++) {
			const token = preset.tokenMap[y][x];
			const tokenNum = typeof token === 'number' ? token : parseInt(token.toString());

			if (tokenNum === doorToken) {
				// Проверяем, находится ли дверь на краю
				const isOnEdge = x === 0 || x === preset.width - 1 || y === 0 || y === preset.height - 1;
				if (isOnEdge) {
					doorPositions.push([x, y]);
				}
			}
		}
	}

	return doorPositions;
}

// Создает go back зоны в радиусе 2 тайлов от каждой двери
function createGoBackZones(doorPositions: Point[], width: number, height: number): Point[] {
	const goBackZones: Point[] = [];
	const addedPositions = new Set<string>();

	for (const [doorX, doorY] of doorPositions) {
		// Создаем зону в радиусе 2 тайлов от двери
		for (let dy = -2; dy <= 2; dy++) {
			for (let dx = -2; dx <= 2; dx++) {
				const x = doorX + dx;
				const y = doorY + dy;

				// Проверяем границы карты
				if (x >= 0 && x < width && y >= 0 && y < height) {
					const posKey = `${x},${y}`;
					if (!addedPositions.has(posKey)) {
						goBackZones.push([x, y]);
						addedPositions.add(posKey);
					}
				}
			}
		}
	}

	return goBackZones;
}

// Создает спавн позицию на расстоянии 1-3 тайлов от ближайшей двери
function createSpawnPosition(doorPositions: Point[], width: number, height: number): Point {
	if (doorPositions.length === 0) {
		// Если дверей нет, спавним в центре карты
		return [Math.floor(width / 2), Math.floor(height / 2)];
	}

	// Берем первую дверь как референс
	const [doorX, doorY] = doorPositions[0];

	// Ищем подходящую позицию в радиусе 1-3 тайлов от двери
	const candidates: Point[] = [];

	for (let distance = 1; distance <= 3; distance++) {
		for (let dy = -distance; dy <= distance; dy++) {
			for (let dx = -distance; dx <= distance; dx++) {
				// Проверяем, что расстояние именно равно текущему distance
				const actualDistance = Math.abs(dx) + Math.abs(dy);
				if (actualDistance !== distance) continue;

				const x = doorX + dx;
				const y = doorY + dy;

				// Проверяем границы карты
				if (x >= 0 && x < width && y >= 0 && y < height) {
					candidates.push([x, y]);
				}
			}
		}

		// Если нашли кандидатов на этом расстоянии, выбираем первого
		if (candidates.length > 0) {
			return candidates[0];
		}
	}

	// Если не нашли подходящую позицию, возвращаем позицию рядом с дверью
	return [doorX, doorY];
}